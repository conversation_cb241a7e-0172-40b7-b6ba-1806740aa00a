# MJ和Flux任务轮询统一整合方案

## 概述

本次整合将MJ（Midjourney）和Flux两个任务轮询系统统一到一个MQ topic和tag中，通过消息内容来区分不同的任务类型，实现了代码复用和维护简化。

## 主要变更

### 1. 统一消息体结构

**文件**: `src/main/java/com/lx/pl/dto/mq/MjTaskPollingVo.java`

- 添加了 `taskType` 字段来区分MJ和Flux任务
- 添加了 `pollingUrl` 字段专门用于Flux任务的轮询URL
- 提供了两种构造函数分别用于MJ和Flux任务
- 定义了任务类型常量 `TaskType.MJ` 和 `TaskType.FLUX`

### 2. 创建统一轮询服务

**文件**: `src/main/java/com/lx/pl/service/UnifiedTaskPollingService.java`

核心功能：
- `startMjTaskStatusPolling()`: 启动MJ任务轮询
- `startFluxTaskStatusPolling()`: 启动Flux任务轮询，支持pollingUrl
- `handleTaskStatusPolling()`: 统一的任务轮询处理入口
- `handleMjTaskPolling()`: 处理MJ任务轮询逻辑
- `handleFluxTaskPolling()`: 处理Flux任务轮询逻辑，支持pollingUrl查询
- `updateFluxTaskStatusInRedis()`: 更新Flux任务状态到Redis

### 3. 修改MidjourneyService

**文件**: `src/main/java/com/lx/pl/service/MidjourneyService.java`

变更内容：
- 添加了 `UnifiedTaskPollingService` 依赖
- 简化了 `startTaskStatusPolling()` 方法，委托给统一服务
- 删除了原有的轮询相关私有方法
- 公开了 `updateMjTaskStatusInRedis()` 方法供统一服务调用

### 4. 修改FluxService

**文件**: `src/main/java/com/lx/pl/service/FluxService.java`

变更内容：
- 添加了 `UnifiedTaskPollingService` 依赖
- 添加了 `getTaskResultByPollingUrl()` 方法支持pollingUrl查询
- 修改了 `startTaskStatusPolling()` 方法，委托给统一服务并传递pollingUrl
- 添加了 `NormalMessageProducer` 依赖

### 5. 修改FluxApiClient

**文件**: `src/main/java/com/lx/pl/client/FluxApiClient.java`

变更内容：
- 添加了 `getTaskResultByPollingUrl()` 接口方法，支持通过完整URL查询任务状态

### 6. 更新消息消费者

**文件**: `src/main/java/com/lx/pl/mq/consumer/MjTaskPollingListener.java`

变更内容：
- 修改依赖从 `MidjourneyService` 改为 `UnifiedTaskPollingService`
- 更新消息处理逻辑，调用统一的处理方法

**文件**: `src/main/java/com/lx/pl/mq/FluxMessageConsumer.java`

变更内容：
- 注释掉了原有的 `FluxPollingConsumer`，因为现在使用统一的消费者

## 技术特性

### 1. 统一MQ配置

- **Topic**: 使用MJ的topic配置 `${rocketmq.midjourney.polling.topic}`
- **Tag**: 使用MJ的tag配置 `${rocketmq.midjourney.polling.tag}`
- **Group**: 使用MJ的group配置 `${rocketmq.midjourney.polling.group}`

### 2. 任务类型区分

通过消息体中的 `taskType` 字段区分：
- `"MJ"`: Midjourney任务
- `"FLUX"`: Flux任务

### 3. Flux pollingUrl支持

- Flux任务创建时会返回 `pollingUrl`
- 统一轮询服务优先使用 `pollingUrl` 查询状态
- 如果没有 `pollingUrl`，则使用默认的taskId查询方式

### 4. Redis状态管理

统一的Redis状态码：
- `-1`: 新建/未知状态
- `0`: 执行中
- `1`: 排队中
- `2`: 失败
- `3`: 成功

## 配置要求

### 1. MQ配置

确保以下配置项存在：
```properties
# Midjourney轮询队列配置（现在也用于Flux）
rocketmq.midjourney.polling.topic=tp_midjourney_polling_prod
rocketmq.midjourney.polling.group=gid_midjourney_polling_prod
rocketmq.midjourney.polling.tag=tag_midjourney_polling_prod
```

### 2. Flux配置

确保FluxConfig中包含以下配置：
- `maxPollingAttempts`: 最大轮询次数（默认100）
- `pollingIntervalSeconds`: 轮询间隔秒数（默认2）
- `firstDelaySeconds`: 首次轮询延迟秒数

## 向后兼容性

1. **MJ任务**: 完全兼容现有的MJ任务处理逻辑
2. **Flux任务**: 保持现有的Flux任务处理逻辑，增加了pollingUrl支持
3. **配置**: 复用现有的MJ MQ配置，无需额外配置

## 测试

创建了完整的单元测试：
- `src/test/java/com/lx/pl/service/UnifiedTaskPollingServiceTest.java`

测试覆盖：
- MJ任务轮询启动
- Flux任务轮询启动（带pollingUrl）
- 任务成功处理
- 任务失败处理
- 任务超时处理
- 未知任务类型处理

## 部署注意事项

1. **顺序部署**: 建议先部署新代码，再停止旧的Flux轮询消费者
2. **监控**: 关注MQ消息消费情况，确保新的统一消费者正常工作
3. **回滚**: 如需回滚，可以重新启用 `FluxMessageConsumer` 中的 `FluxPollingConsumer`

## 优势

1. **代码复用**: 减少重复的轮询逻辑代码
2. **维护简化**: 统一的轮询处理逻辑，便于维护和调试
3. **资源优化**: 减少MQ topic和consumer数量
4. **扩展性**: 便于后续添加新的AI模型任务类型
5. **一致性**: 统一的状态管理和错误处理机制
