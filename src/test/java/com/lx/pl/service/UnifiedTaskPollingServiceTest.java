package com.lx.pl.service;

import com.lx.pl.dto.mq.MjTaskPollingVo;
import com.lx.pl.dto.flux.FluxResponse;
import com.lx.pl.dto.midjourney.MidjourneyResponse;
import com.lx.pl.enums.FluxTaskStatus;
import com.lx.pl.enums.MidjourneyTaskStatus;
import com.lx.pl.service.redis.RedisService;
import com.lx.pl.config.MidjourneyConfig;
import com.lx.pl.config.FluxConfig;
import com.lx.pl.mq.producer.NormalMessageProducer;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 统一任务轮询服务测试
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class UnifiedTaskPollingServiceTest {

    @Mock
    private MidjourneyService midjourneyService;

    @Mock
    private FluxService fluxService;

    @Mock
    private FluxCallbackService fluxCallbackService;

    @Mock
    private RedisService redisService;

    @Mock
    private NormalMessageProducer normalMessageProducer;

    @Mock
    private MidjourneyConfig midjourneyConfig;

    @Mock
    private FluxConfig fluxConfig;

    @InjectMocks
    private UnifiedTaskPollingService unifiedTaskPollingService;

    @BeforeEach
    void setUp() {
        // 设置默认配置
        when(midjourneyConfig.getMaxPollingAttempts()).thenReturn(30);
        when(midjourneyConfig.getPollingIntervalSeconds()).thenReturn(2);
        when(fluxConfig.getMaxPollingAttempts()).thenReturn(100);
        when(fluxConfig.getPollingIntervalSeconds()).thenReturn(2);
    }

    @Test
    void testStartMjTaskStatusPolling() {
        // Given
        String jobId = "test-mj-job-id";
        String loginName = "test-user";
        long delaySeconds = 2;

        // When
        unifiedTaskPollingService.startMjTaskStatusPolling(jobId, loginName, delaySeconds);

        // Then
        verify(midjourneyService).updateMjTaskStatusInRedis(jobId, 0);
        verify(normalMessageProducer).syncDelaySend(any(), eq(delaySeconds));
    }

    @Test
    void testStartFluxTaskStatusPolling() {
        // Given
        String taskId = "test-flux-task-id";
        String loginName = "test-user";
        Integer delaySeconds = 2;
        String pollingUrl = "https://api.bfl.ai/v1/get_result?id=test-flux-task-id";

        // When
        unifiedTaskPollingService.startFluxTaskStatusPolling(taskId, loginName, delaySeconds, pollingUrl);

        // Then
        verify(normalMessageProducer).syncDelaySend(any(), eq(delaySeconds.longValue()));
    }

    @Test
    void testHandleMjTaskPolling_Success() {
        // Given
        String jobId = "test-mj-job-id";
        String loginName = "test-user";
        MjTaskPollingVo pollingVo = new MjTaskPollingVo(jobId, loginName, 0, 30, 2000, System.currentTimeMillis());

        MidjourneyResponse.TaskStatusResponse taskStatus = new MidjourneyResponse.TaskStatusResponse();
        taskStatus.setStatus("SUCCESS");

        when(redisService.hasKey(anyString())).thenReturn(true);
        when(midjourneyService.getTaskStatus(jobId)).thenReturn(taskStatus);

        // When
        unifiedTaskPollingService.handleTaskStatusPolling(pollingVo);

        // Then
        verify(midjourneyService).handleTaskSuccess(eq(jobId), eq(loginName), eq(taskStatus));
    }

    @Test
    void testHandleFluxTaskPolling_Success() {
        // Given
        String taskId = "test-flux-task-id";
        String loginName = "test-user";
        String pollingUrl = "https://api.bfl.ai/v1/get_result?id=test-flux-task-id";
        MjTaskPollingVo pollingVo = new MjTaskPollingVo(
                MjTaskPollingVo.TaskType.FLUX,
                taskId,
                loginName,
                0,
                100,
                2000,
                System.currentTimeMillis(),
                pollingUrl
        );

        FluxResponse.TaskStatusResponse taskStatus = new FluxResponse.TaskStatusResponse();
        taskStatus.setStatus("Ready");
        taskStatus.setProgress(100);

        when(redisService.stringGet("flux:task:" + taskId)).thenReturn("test-mark-id");
        when(redisService.stringGet("test-mark-id")).thenReturn(loginName);
        when(fluxService.getTaskResultByPollingUrl(pollingUrl)).thenReturn(taskStatus);

        // When
        unifiedTaskPollingService.handleTaskStatusPolling(pollingVo);

        // Then
        verify(fluxService).handleTaskSuccess(eq(taskId), eq(loginName), eq(taskStatus));
        verify(redisService).putDataToHash(eq(loginName), eq("test-mark-id"), eq(3), any(), any());
    }

    @Test
    void testHandleFluxTaskPolling_WithoutPollingUrl() {
        // Given
        String taskId = "test-flux-task-id";
        String loginName = "test-user";
        MjTaskPollingVo pollingVo = new MjTaskPollingVo(
                MjTaskPollingVo.TaskType.FLUX,
                taskId,
                loginName,
                0,
                100,
                2000,
                System.currentTimeMillis(),
                null // 没有pollingUrl
        );

        FluxResponse.TaskStatusResponse taskStatus = new FluxResponse.TaskStatusResponse();
        taskStatus.setStatus("Running");
        taskStatus.setProgress(50);

        when(redisService.stringGet("flux:task:" + taskId)).thenReturn("test-mark-id");
        when(redisService.stringGet("test-mark-id")).thenReturn(loginName);
        when(fluxService.getTaskResult(taskId)).thenReturn(taskStatus);

        // When
        unifiedTaskPollingService.handleTaskStatusPolling(pollingVo);

        // Then
        verify(fluxService).getTaskResult(eq(taskId));
        verify(redisService).putDataToHash(eq(loginName), eq("test-mark-id"), eq(0), any(), any());
        verify(normalMessageProducer).syncDelaySend(any(), eq(2L)); // 下次轮询
    }

    @Test
    void testHandleMjTaskPolling_Failed() {
        // Given
        String jobId = "test-mj-job-id";
        String loginName = "test-user";
        MjTaskPollingVo pollingVo = new MjTaskPollingVo(jobId, loginName, 0, 30, 2000, System.currentTimeMillis());

        MidjourneyResponse.TaskStatusResponse taskStatus = new MidjourneyResponse.TaskStatusResponse();
        taskStatus.setStatus("FAILED");

        when(redisService.hasKey(anyString())).thenReturn(true);
        when(midjourneyService.getTaskStatus(jobId)).thenReturn(taskStatus);

        // When
        unifiedTaskPollingService.handleTaskStatusPolling(pollingVo);

        // Then
        verify(midjourneyService).handleTaskFailure(eq(jobId), eq(loginName));
    }

    @Test
    void testHandleFluxTaskPolling_Failed() {
        // Given
        String taskId = "test-flux-task-id";
        String loginName = "test-user";
        MjTaskPollingVo pollingVo = new MjTaskPollingVo(
                MjTaskPollingVo.TaskType.FLUX,
                taskId,
                loginName,
                0,
                100,
                2000,
                System.currentTimeMillis(),
                null
        );

        FluxResponse.TaskStatusResponse taskStatus = new FluxResponse.TaskStatusResponse();
        taskStatus.setStatus("Error");

        when(redisService.stringGet("flux:task:" + taskId)).thenReturn("test-mark-id");
        when(redisService.stringGet("test-mark-id")).thenReturn(loginName);
        when(fluxService.getTaskResult(taskId)).thenReturn(taskStatus);

        // When
        unifiedTaskPollingService.handleTaskStatusPolling(pollingVo);

        // Then
        verify(fluxService).handleTaskFailure(eq(taskId), eq(loginName));
        verify(redisService).putDataToHash(eq(loginName), eq("test-mark-id"), eq(2), any(), any());
    }

    @Test
    void testHandleTaskPolling_Timeout() {
        // Given
        String jobId = "test-mj-job-id";
        String loginName = "test-user";
        MjTaskPollingVo pollingVo = new MjTaskPollingVo(jobId, loginName, 29, 30, 2000, System.currentTimeMillis());

        MidjourneyResponse.TaskStatusResponse taskStatus = new MidjourneyResponse.TaskStatusResponse();
        taskStatus.setStatus("PENDING_QUEUE");

        when(redisService.hasKey(anyString())).thenReturn(true);
        when(midjourneyService.getTaskStatus(jobId)).thenReturn(taskStatus);

        // When
        unifiedTaskPollingService.handleTaskStatusPolling(pollingVo);

        // Then
        verify(midjourneyService).handleTaskTimeout(eq(jobId), eq(loginName));
    }

    @Test
    void testHandleTaskPolling_UnknownTaskType() {
        // Given
        MjTaskPollingVo pollingVo = new MjTaskPollingVo();
        pollingVo.setTaskType("UNKNOWN");
        pollingVo.setJobId("test-job-id");
        pollingVo.setLoginName("test-user");

        // When
        unifiedTaskPollingService.handleTaskStatusPolling(pollingVo);

        // Then
        // 应该记录警告日志，但不会调用任何处理方法
        verifyNoInteractions(midjourneyService);
        verifyNoInteractions(fluxService);
    }
}
