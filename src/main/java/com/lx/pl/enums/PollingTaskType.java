package com.lx.pl.enums;

/**
 * Flux任务状态枚举
 *
 * <AUTHOR>
 */
public enum FluxTaskStatus {

    /**
     * 任务未找到
     */
    TASK_NOT_FOUND("Task not found"),

    /**
     * 任务排队中
     */
    PENDING("Pending"),

    /**
     * 任务执行中
     */
    RUNNING("Running"),

    /**
     * 任务成功完成
     */
    READY("Ready"),

    /**
     * 任务失败
     */
    ERROR("Error"),

    /**
     * 任务被取消
     */
    CANCELLED("Cancelled"),

    /**
     * 任务超时
     */
    TIMEOUT("Timeout"),

    /**
     * 未知状态
     */
    UNKNOWN("Unknown");

    private final String status;

    FluxTaskStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }

    /**
     * 根据状态字符串获取枚举值
     */
    public static FluxTaskStatus fromStatus(String status) {
        if (status == null) {
            return UNKNOWN;
        }

        for (FluxTaskStatus taskStatus : values()) {
            if (taskStatus.getStatus().equalsIgnoreCase(status)) {
                return taskStatus;
            }
        }

        return UNKNOWN;
    }

    /**
     * 判断是否为最终状态（不需要继续轮询）
     */
    public boolean isFinalStatus() {
        return this == READY || this == ERROR || this == CANCELLED || this == TIMEOUT || this == TASK_NOT_FOUND;
    }

    /**
     * 判断是否为成功状态
     */
    public boolean isSuccess() {
        return this == READY;
    }

    /**
     * 判断是否为失败状态
     */
    public boolean isFailure() {
        return this == ERROR || this == CANCELLED || this == TIMEOUT || this == TASK_NOT_FOUND;
    }
}
