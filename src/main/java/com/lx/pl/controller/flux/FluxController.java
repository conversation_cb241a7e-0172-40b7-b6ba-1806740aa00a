package com.lx.pl.controller.flux;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.lx.pl.annotation.Authorization;
import com.lx.pl.annotation.CurrentUser;
import com.lx.pl.config.FluxConfig;
import com.lx.pl.constant.LogicConstants;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.dto.GenGenericPara;
import com.lx.pl.dto.Resolution;
import com.lx.pl.dto.flux.FluxResponse;
import com.lx.pl.dto.generic.R;
import com.lx.pl.enums.FeaturesType;
import com.lx.pl.enums.LogicErrorCode;
import com.lx.pl.enums.OriginCreate;
import com.lx.pl.enums.VipType;
import com.lx.pl.exception.LogicException;
import com.lx.pl.manager.PromptTemplateManager;
import com.lx.pl.service.FluxService;
import com.lx.pl.service.GenService;
import com.lx.pl.service.PromptFiltrationService;
import com.lx.pl.service.VipService;
import com.lx.pl.util.AspectRatioUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Flux控制器
 *
 * <AUTHOR>
 */
@Slf4j
@Tag(name = "Flux图像生成接口")
@RestController
@RequestMapping("/api/flux")
public class FluxController {

    @Value("${bad.words.filter}")
    Boolean badWordsFilter;

    @Autowired
    private FluxService fluxService;

    @Autowired
    private FluxConfig fluxConfig;

    @Autowired
    private GenService genService;

    @Autowired
    private VipService vipService;

    @Autowired
    private PromptFiltrationService promptFiltrationService;

    @Autowired
    private PromptTemplateManager promptTemplateManager;

    /**
     * Kontext Pro图像生成请求参数
     */
    @Data
    public static class KontextProParams {
        @NotBlank(message = "提示词不能为空")
        private String prompt;

        private String aspectRatio; // 宽高比

        private Long seed; // 种子值

        private String outputFormat = "jpeg"; // 输出格式

        private Boolean promptUpsampling = false; // 是否启用prompt增强

        private Integer safetyTolerance = 2; // 安全容忍度
    }

    /**
     * 图像编辑请求参数
     */
    @Data
    public static class ImageEditParams {
        @NotBlank(message = "提示词不能为空")
        private String prompt;

        @NotBlank(message = "输入图像不能为空")
        private String inputImage; // Base64编码的图像

        private Double strength = 0.8; // 编辑强度

        private Double guidance = 7.5; // 编辑指导

        private String aspectRatio; // 宽高比

        private Integer seed; // 种子值
    }

    /**
     * 创建Flux Kontext Pro图像生成任务
     */
    @PostMapping("/kontext-pro")
    @Operation(summary = "Flux Kontext Pro图像生成")
    @Authorization
    public R<FluxResponse.CreateTaskResponse> createKontextProTask(
            @Parameter(hidden = true) @CurrentUser User user,
            @RequestBody @Valid KontextProParams params) {

        log.info("Flux Kontext Pro request from user: {}, prompt: {}",
                user.getLoginName(), params.getPrompt());

        // 检查并发任务数限制
        if (fluxService.checkFluxConcurrentJobs(user, null)) {
            throw new LogicException(LogicErrorCode.FLUX_EXCEED_CONCURRENT_JOBS);
        }

        // 构建GenGenericPara参数
        GenGenericPara genParameters = new GenGenericPara();
        genParameters.setPrompt(params.getPrompt());
//        genParameters.setAspectRatio(params.getAspectRatio());
        genParameters.setSeed(params.getSeed());

        FluxResponse.CreateTaskResponse result = fluxService.createKontextProTask(
                params.getPrompt(),
                user,
                null, // markId为空，不保存到数据库
                false, // fastHour
                "web", // platform
                genParameters
        );

        return R.success(result);
    }

    /**
     * 获取任务结果
     */
    @GetMapping("/result/{taskId}")
    @Operation(summary = "获取Flux任务结果")
    @Authorization
    public R<FluxResponse.TaskStatusResponse> getTaskResult(
            @Parameter(hidden = true) @CurrentUser User user,
            @PathVariable String taskId) {

        log.info("Get Flux task result request from user: {}, taskId: {}",
                user.getLoginName(), taskId);

        FluxResponse.TaskStatusResponse result = fluxService.getTaskResult(taskId);
        return R.success(result);
    }

    /**
     * 兼容原GenController的create接口
     */
    @PostMapping("/create")
    @Operation(summary = "Flux图片生成（兼容原接口）")
    @Authorization
    public R<Map<String, Object>> createCompatible(
            @Parameter(hidden = true) @CurrentUser User user,
            @RequestBody GenGenericPara genParameters,
            HttpServletRequest request) throws IOException {

        // 基础参数验证
        if (genParameters.getPrompt() == null) {
            genParameters.setPrompt("");
        }

        // Prompt过滤验证
        Boolean filterChildSex = promptFiltrationService.filterChildSex(genParameters.getPrompt());
        if (filterChildSex) {
            throw new LogicException(LogicErrorCode.ILLEGAL_PROMPT);
        }

        // 平台验证
        String platform = genService.getPlatform(request);
        if (StringUtil.isBlank(platform)) {
            throw new LogicException(LogicErrorCode.ILLEGAL_REQUEST);
        }

        // 是否开启badword过滤
        if (platform.equals("android") && badWordsFilter) {
            Boolean filterBadWords = promptFiltrationService.filterBadWords(genParameters.getPrompt());
            if (filterBadWords) {
                throw new LogicException(LogicErrorCode.ILLEGAL_PROMPT);
            }
        }

        if (null == genParameters.getNegative_prompt()) {
            genParameters.setNegative_prompt("");
        }
        if (genParameters.getPrompt().length() > 3000) {
            return R.fail(400, "Image prompt exceeds 3000!");
        }
        if (null == genParameters.getResolution()) {
            return R.fail(400, "Image dimensions required !");
        }
        if (0 >= genParameters.getResolution().getWidth()) {
            return R.fail(400, "Image width required !");
        }
        if (0 >= genParameters.getResolution().getHeight()) {
            return R.fail(400, "Image height required !");
        }
        if (2048 < genParameters.getResolution().getWidth()) {
            return R.fail(400, "Image width exceeds 2048 pixels !");
        }
        if (2048 < genParameters.getResolution().getHeight()) {
            return R.fail(400, "Image height exceeds 2048 pixels !");
        }
        if (1 > genParameters.getResolution().getBatch_size()) {
            return R.fail(400, "At least one image required !");
        }

        if (!genService.checkPlatformModel(genParameters.getModel_id(), request)) {
            return R.fail(400, "Model not support !");
        }

        //设置生图张数固定为1张
        genParameters.getResolution().setBatch_size(1);

        // 判断是否是普通用户
        Boolean notVip = VipType.basic.getValue().equals(user.getVipType());

        // 判断是否fast模式 - 对于Midjourney，我们使用默认的模型ID
//        Boolean fastHour = vipService.judgeUserFastCreate("midjourney", user, 1, Boolean.FALSE, OriginCreate.create, null, null);
        Boolean fastHour = true;

        //检查权限
        if (notVip) {
            throw new LogicException(LogicErrorCode.NOT_ENOUGH_LUMENS_SUPPORT);
        }

        //校验点数
        if (!vipService.judgeUserFastCreate(genParameters.getModel_id(), user, genParameters.getResolution().getBatch_size(), Boolean.FALSE, OriginCreate.create, null, null)) {
            throw new LogicException(LogicErrorCode.NOT_ENOUGH_LUMENS_SUPPORT);
        }

        // 设置传参
        String markId = LogicConstants.TTAPI_MARKID_PREFIX + UUID.randomUUID();
        if (genService.checkUserConcurrentJobs(user, markId, false)) {
            throw new LogicException(LogicErrorCode.EXCEED_CONCURRENT_JOBS);
        }

        // 检查Flux并发任务数限制
        if (fluxService.checkFluxConcurrentJobs(user, null)) {
            throw new LogicException(LogicErrorCode.FLUX_EXCEED_CONCURRENT_JOBS);
        }

        // 判断生图类型
        String feature = FeaturesType.ttp.getValue();
        if (genParameters.getImgEditPara() != null && genParameters.getImgEditPara().getFileId() != null) {
            feature = FeaturesType.edit.getValue();
        }


        // 调用Flux API
        FluxResponse.CreateTaskResponse fluxResult = fluxService.createKontextProTask(
                genParameters.getPrompt(),
                user,
                markId,
                fastHour,
                platform,
                genParameters
        );

        // 构建返回结果
        Map<String, Object> result = new HashMap<>();
        result.put("markId", markId);
        result.put("taskId", fluxResult.getId());
        result.put("feature", feature);
        result.put("originCreate", OriginCreate.create.getValue());

        return R.success(result);
    }

//    /**
//     * 批量处理任务状态查询
//     */
//    @PostMapping("/batchProcessTask")
//    @Operation(summary = "批量处理Flux任务状态")
//    @Authorization
//    public R<Map<String, Object>> batchProcessTask(
//            @Parameter(hidden = true) @CurrentUser User user,
//            @RequestBody Map<String, Object> requestBody) {
//
//        String markId = (String) requestBody.get("markId");
//        if (StringUtil.isBlank(markId)) {
//            throw new LogicException(LogicErrorCode.INVALID_PARAMETER);
//        }
//
//        log.info("Batch process Flux task request from user: {}, markId: {}",
//                user.getLoginName(), markId);
//
//        // 调用GenService的批量处理方法
//        return genService.batchProcessTask(user, requestBody);
//    }
}
