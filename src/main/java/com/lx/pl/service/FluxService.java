package com.lx.pl.service;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.lx.pl.client.FluxApiClient;
import com.lx.pl.config.FluxConfig;
import com.lx.pl.constant.LockPrefixConstant;
import com.lx.pl.db.mysql.gen.entity.PromptRecord;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.db.mysql.gen.mapper.PromptFileMapper;
import com.lx.pl.db.mysql.gen.mapper.PromptRecordMapper;
import com.lx.pl.db.mysql.gen.mapper.UserMapper;
import com.lx.pl.dto.GenGenericPara;
import com.lx.pl.dto.Resolution;
import com.lx.pl.dto.flux.FluxRequest;
import com.lx.pl.dto.flux.FluxResponse;
import com.lx.pl.enums.LogicErrorCode;
import com.lx.pl.enums.OriginCreate;
import com.lx.pl.exception.LogicException;
import com.lx.pl.mq.producer.NormalMessageProducer;
import com.lx.pl.util.AspectRatioUtils;
import com.lx.pl.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import retrofit2.Call;
import retrofit2.Response;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static com.lx.pl.service.LoadBalanceService.USER_TASK_TIMESTAMP;

/**
 * Flux服务类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class FluxService {

    @Autowired
    private FluxApiClient fluxApiClient;

    @Autowired
    private FluxConfig fluxConfig;

    @Autowired
    private RedisService redisService;

    @Autowired
    private PromptRecordMapper promptRecordMapper;

    @Autowired
    private PromptFileMapper promptFileMapper;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private VipService vipService;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private NormalMessageProducer normalMessageProducer;

    @Autowired
    private UnifiedTaskPollingService unifiedTaskPollingService;

    @Value("${flux.modelId}")
    String fluxModelId;

    @Value("${rocketmq.image.process.tag}")
    private String fluxImageProcessTag;

    @Value("${rocketmq.piclumen.topic}")
    private String fluxImageProcessTopic;

    // Redis key常量
    private static final String FLUX_TASK_LOCK = "flux_task_";
    private static final String FLUX_TASK_PREFIX = "flux:task:";
    private static final String FLUX_USER_CONCURRENT_PREFIX = "flux:concurrent";
    private static final String FLUX_IMG_PREFIX = "flux:do_img:";

    /**
     * 创建Flux Kontext Pro图像生成任务
     *
     * @param prompt        提示词
     * @param user          用户
     * @param markId        标记ID
     * @param fastHour      是否快速生图
     * @param platform      平台
     * @param genParameters 原始生图参数
     * @return 任务响应
     */
    public FluxResponse.CreateTaskResponse createKontextProTask(String prompt, User user,
                                                                String markId, Boolean fastHour, String platform, GenGenericPara genParameters) {
        try {
            // 构建请求
            FluxRequest.KontextProRequest request = new FluxRequest.KontextProRequest();
            request.setPrompt(genParameters.getPrompt());
            request.setOutputFormat(fluxConfig.getDefaultOutputFormat());
            request.setSafetyTolerance(fluxConfig.getDefaultSafetyTolerance());
            request.setPromptUpsampling(fluxConfig.getPromptUpsamplingEnabled());

            // 设置宽高比
            if (genParameters.getResolution() != null) {
                Resolution resolution = genParameters.getResolution();
                if (resolution.getWidth() > 0 && resolution.getHeight() > 0) {
                    // 计算宽高比
                    String aspectRatio = AspectRatioUtils.getAspectRatioLabel(resolution.getWidth(), resolution.getHeight());
                    if (StringUtil.isNotBlank(aspectRatio)) {
                        request.setAspectRatio(aspectRatio);
                    }
                }
            }


            // 设置种子值
            if (genParameters != null && genParameters.getSeed() != null) {
                request.setSeed(genParameters.getSeed());
            }

            log.info("Flux Kontext Pro request: {}", JsonUtils.writeToString(request));

            // 调用API
            Call<FluxResponse.CreateTaskResponse> call = fluxApiClient.createKontextProTask(fluxConfig.getApiKey(), request);
            Response<FluxResponse.CreateTaskResponse> response = call.execute();

            if (!response.isSuccessful()) {
                String errorBody = response.errorBody() != null ? response.errorBody().string() : "Unknown error";
                log.error("Flux Kontext Pro API call failed: {}", errorBody);
                throw new LogicException(LogicErrorCode.FLUX_API_ERROR);
            }

            FluxResponse.CreateTaskResponse responseData = response.body();
            if (responseData == null || StringUtil.isBlank(responseData.getId())) {
                log.error("Flux Kontext Pro API response is null or missing task ID");
                throw new LogicException(LogicErrorCode.FLUX_API_ERROR);
            }

            // 添加到并发任务列表
            String userConcurrentKey = FLUX_USER_CONCURRENT_PREFIX;
            redisService.putDataToHash(userConcurrentKey, responseData.getId(), System.currentTimeMillis());

            // 如果提供了markId等参数，则保存PromptRecord到数据库
            if (StringUtil.isNotBlank(markId)) {
                savePromptRecord(responseData.getId(), prompt, user, markId, fastHour, platform, genParameters);

                // 保存Flux任务状态到Redis
                saveFluxTaskStatusToRedis(responseData.getId(), markId, user.getLoginName());

                // 启动任务状态轮询，传递pollingUrl
                startTaskStatusPolling(responseData.getId(), user.getLoginName(), fluxConfig.getFirstDelaySeconds(), responseData.getPollingUrl());
            }

            log.info("Flux Kontext Pro task created successfully, taskId: {}", responseData.getId());
            return responseData;

        } catch (IOException e) {
            log.error("Flux Kontext Pro API call failed", e);
            throw new LogicException(LogicErrorCode.FLUX_API_ERROR);
        } catch (Exception e) {
            log.error("Create Flux Kontext Pro task error", e);
            throw new LogicException(LogicErrorCode.FLUX_API_ERROR);
        }
    }

    /**
     * 获取任务结果
     */
    public FluxResponse.TaskStatusResponse getTaskResult(String taskId) {
        try {
            Call<FluxResponse.TaskStatusResponse> call = fluxApiClient.getTaskResult(taskId);
            Response<FluxResponse.TaskStatusResponse> response = call.execute();

            if (!response.isSuccessful()) {
                String errorBody = response.errorBody() != null ? response.errorBody().string() : "Unknown error";
                log.error("Flux get task result API call failed: {}", errorBody);
                throw new LogicException(LogicErrorCode.FLUX_API_ERROR);
            }

            return response.body();

        } catch (IOException e) {
            log.error("Flux get task result API call failed", e);
            throw new LogicException(LogicErrorCode.FLUX_API_ERROR);
        }
    }

    /**
     * 通过pollingUrl获取任务结果
     */
    public FluxResponse.TaskStatusResponse getTaskResultByPollingUrl(String pollingUrl) {
        try {
            Call<FluxResponse.TaskStatusResponse> call = fluxApiClient.getTaskResultByPollingUrl(pollingUrl);
            Response<FluxResponse.TaskStatusResponse> response = call.execute();

            if (!response.isSuccessful()) {
                String errorBody = response.errorBody() != null ? response.errorBody().string() : "Unknown error";
                log.error("Flux get task result by polling URL API call failed: {}", errorBody);
                throw new LogicException(LogicErrorCode.FLUX_API_ERROR);
            }

            return response.body();

        } catch (IOException e) {
            log.error("Flux get task result by polling URL API call failed", e);
            throw new LogicException(LogicErrorCode.FLUX_API_ERROR);
        }
    }

    /**
     * 保存PromptRecord到数据库
     */
    private void savePromptRecord(String taskId, String prompt, User user, String markId,
                                  Boolean fastHour, String platform, GenGenericPara genParameters) {
        try {
            PromptRecord promptRecord = new PromptRecord();
            promptRecord.setLoginName(user.getLoginName());
            promptRecord.setPromptId(taskId);
            promptRecord.setPrompt(prompt);
            promptRecord.setNegativePrompt(""); // Flux没有负面提示词
            promptRecord.setCreateBy(user.getLoginName());
            promptRecord.setCreateTime(LocalDateTime.now());
            promptRecord.setGenStartTime(LocalDateTime.now());
            promptRecord.setOriginCreate(OriginCreate.create.getValue());
            promptRecord.setBatchSize(1); // Flux每次生成1张图
            promptRecord.setModelId(fluxModelId);
            promptRecord.setMarkId(markId);

            if (genParameters != null) {
                promptRecord.setAspectRatio(genParameters.getResolution().getWidth() + " * " + genParameters.getResolution().getHeight());
                promptRecord.setPromptParams(JsonUtils.convertToJsonNode(genParameters));
            }

            promptRecord.setFeatureName("flux-kontext-pro");
            promptRecord.setFastHour(fastHour != null ? fastHour : false);
            promptRecord.setPlatform(platform);

            promptRecordMapper.insert(promptRecord);
            log.info("Saved PromptRecord for Flux task: {}", taskId);

        } catch (Exception e) {
            log.error("Save PromptRecord error for task: " + taskId, e);
        }
    }

    /**
     * 保存Flux任务状态到Redis
     */
    private void saveFluxTaskStatusToRedis(String taskId, String markId, String loginName) {
        try {
            // 1. 保存taskId -> markId的映射
            redisService.stringSet(taskId, markId, 2, TimeUnit.HOURS);

            // 2. 保存markId -> loginName的映射
            redisService.stringSet(markId, loginName, 2, TimeUnit.HOURS);

            // 3. 在用户hash中设置任务状态为-1（新建状态）
            redisService.putDataToHash(loginName, markId, -1, 2, TimeUnit.HOURS);

            // 4. 设置任务时间戳
            redisService.set(USER_TASK_TIMESTAMP + markId, System.currentTimeMillis(), 2, TimeUnit.HOURS);

            String taskKey = FLUX_TASK_PREFIX + taskId;
            redisService.stringSet(taskKey, markId, 10, TimeUnit.MINUTES);

            log.debug("Saved Flux task status to Redis: taskId={}, markId={}, loginName={}", taskId, markId, loginName);

        } catch (Exception e) {
            log.error("Save Flux task status to Redis error", e);
        }
    }

    /**
     * 启动任务状态轮询（使用统一轮询服务）
     */
    private void startTaskStatusPolling(String taskId, String loginName, Integer delaySeconds, String pollingUrl) {
        // 使用统一的轮询服务
        unifiedTaskPollingService.startFluxTaskStatusPolling(taskId, loginName, delaySeconds, pollingUrl);
    }

    /**
     * 检查用户Flux并发任务数是否超过限制
     */
    public boolean checkFluxConcurrentJobs(User user, String taskId) {
        String lockKey = LockPrefixConstant.CONCURRENT_EXECUTION_LOCK_PREFIX + "flux:" + user.getId();
        RLock lock = redissonClient.getLock(lockKey);

        try {
            lock.lock();

            // 获取用户当前的Flux任务列表
            String userConcurrentKey = FLUX_USER_CONCURRENT_PREFIX;
            List<String> userTaskList = redisService.getAllKeysFromHash(userConcurrentKey);

            // 清理已过期的任务
            cleanExpiredFluxTasks(userConcurrentKey, userTaskList);

            // 重新获取清理后的任务列表
            userTaskList = redisService.getAllKeysFromHash(userConcurrentKey);

            // 检查是否超过最大并发数限制
            int maxConcurrentJobs = fluxConfig.getMaxConcurrentJobs() != null ?
                    fluxConfig.getMaxConcurrentJobs() : 10;

            if (userTaskList.size() >= maxConcurrentJobs) {
                log.warn("User {} Flux concurrent jobs limit exceeded: {}/{}",
                        user.getLoginName(), userTaskList.size(), maxConcurrentJobs);
                return true; // 超过限制
            }

            return false; // 未超过限制

        } finally {
            lock.unlock();
        }
    }

    /**
     * 清理已过期的Flux任务
     */
    private void cleanExpiredFluxTasks(String userConcurrentKey, List<String> taskList) {
        if (CollectionUtils.isEmpty(taskList)) {
            return;
        }

        long currentTime = System.currentTimeMillis();
        long expireTime = 2 * 60 * 60 * 1000; // 2小时过期

        for (String taskId : taskList) {
            try {
                Object timestamp = redisService.getDataFromHash(userConcurrentKey, taskId);
                if (timestamp != null) {
                    long taskTime = Long.parseLong(timestamp.toString());
                    if (currentTime - taskTime > expireTime) {
                        redisService.deleteFieldFromHash(userConcurrentKey, taskId);
                        log.debug("Cleaned expired Flux task: {}", taskId);
                    }
                }
            } catch (Exception e) {
                log.warn("Clean expired Flux task error: {}", taskId, e);
                redisService.deleteFieldFromHash(userConcurrentKey, taskId);
            }
        }
    }

    /**
     * 移除用户的Flux并发任务
     */
    public void removeFluxConcurrentJob(String loginName, String taskId) {
        try {
            String userConcurrentKey = FLUX_USER_CONCURRENT_PREFIX;
            redisService.deleteFieldFromHash(userConcurrentKey, taskId);
            log.debug("Removed Flux concurrent job: {} for user: {}", taskId, loginName);
        } catch (Exception e) {
            log.error("Remove Flux concurrent job error", e);
        }
    }

    /**
     * 处理任务成功
     */
    public void handleTaskSuccess(String taskId, String loginName, FluxResponse.TaskStatusResponse taskStatus) {
        RLock lock = redissonClient.getLock(FLUX_TASK_LOCK + taskId);

        try {
            lock.lock();
            // 图片处理锁-防止重复处理
            String taskKey = FLUX_IMG_PREFIX + taskId;
            String s = redisService.stringGet(taskKey);
            if (StringUtil.isNotBlank(s)) {
                return;
            }

            redisService.stringSet(taskKey, taskId, 5, TimeUnit.MINUTES);

            if (promptRecordFinished(taskId, loginName)) {
                return;
            }

            FluxResponse.TaskResult taskResult = taskStatus.getResult();
            if (taskResult == null) {
                log.warn("Task result is null for successful taskId: {}", taskId);
                return;
            }

            // 发送图片处理MQ消息
            if (StringUtil.isNotBlank(taskResult.getSample())) {
                sendImageProcessMessage(taskId, loginName, taskResult);
            }

            log.info("Successfully processed Flux task completion for taskId: {}", taskId);

        } finally {
            lock.unlock();
        }
    }

    /**
     * 处理任务失败
     */
    public void handleTaskFailure(String taskId, String loginName) {
        try {
            // 更新PromptRecord状态为失败
            String markId = redisService.stringGet(taskId);
            if (StringUtil.isNotBlank(markId)) {
                LambdaUpdateWrapper<PromptRecord> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(PromptRecord::getPromptId, taskId)
                        .set(PromptRecord::getFailureMessage, "Flux task failed")
                        .set(PromptRecord::getGenEndTime, LocalDateTime.now());
                promptRecordMapper.update(null, updateWrapper);

                // 更新Redis状态
                redisService.putDataToHash(loginName, markId, 2); // 2表示失败
            }

            // 移除并发任务
            removeFluxConcurrentJob(loginName, taskId);

            // 清理Redis数据
            cleanupTaskFromRedis(taskId, markId, loginName);

            log.info("Handled Flux task failure for taskId: {}", taskId);

        } catch (Exception e) {
            log.error("Handle Flux task failure error", e);
        }
    }

    /**
     * 轮询消息数据类
     */
    public static class FluxPollingMessage {
        private String taskId;
        private String loginName;
        private Integer attemptCount;

        public FluxPollingMessage() {
        }

        public FluxPollingMessage(String taskId, String loginName, Integer attemptCount) {
            this.taskId = taskId;
            this.loginName = loginName;
            this.attemptCount = attemptCount;
        }

        // getters and setters
        public String getTaskId() {
            return taskId;
        }

        public void setTaskId(String taskId) {
            this.taskId = taskId;
        }

        public String getLoginName() {
            return loginName;
        }

        public void setLoginName(String loginName) {
            this.loginName = loginName;
        }

        public Integer getAttemptCount() {
            return attemptCount;
        }

        public void setAttemptCount(Integer attemptCount) {
            this.attemptCount = attemptCount;
        }
    }

    /**
     * 检查PromptRecord是否已完成
     */
    private boolean promptRecordFinished(String taskId, String loginName) {
        try {
            PromptRecord promptRecord = promptRecordMapper.selectOne(
                    new LambdaUpdateWrapper<PromptRecord>()
                            .eq(PromptRecord::getPromptId, taskId)
                            .eq(PromptRecord::getLoginName, loginName)
            );

            return promptRecord != null && promptRecord.getGenEndTime() != null;

        } catch (Exception e) {
            log.error("Check PromptRecord finished error", e);
            return false;
        }
    }

    /**
     * 发送图片处理MQ消息
     */
    private void sendImageProcessMessage(String taskId, String loginName, FluxResponse.TaskResult taskResult) {
        try {
            FluxImageProcessMessage message = new FluxImageProcessMessage();
            message.setTaskId(taskId);
            message.setLoginName(loginName);
            message.setImageUrl(taskResult.getSample());
            message.setSeed(taskResult.getSeed());

            String messageBody = JsonUtils.writeToString(message);
            normalMessageProducer.syncSend(fluxImageProcessTopic, fluxImageProcessTag, messageBody);

            log.debug("Sent Flux image process message for taskId: {}", taskId);

        } catch (Exception e) {
            log.error("Send Flux image process message error", e);
        }
    }

    /**
     * 清理Redis中的任务数据
     */
    private void cleanupTaskFromRedis(String taskId, String markId, String loginName) {
        try {
            if (StringUtil.isNotBlank(taskId)) {
                redisService.delete(taskId);
                String taskKey = FLUX_TASK_PREFIX + taskId;
                redisService.delete(taskKey);
            }

            if (StringUtil.isNotBlank(markId)) {
                redisService.delete(markId);
                redisService.deleteFieldFromHash(loginName, markId);
                redisService.delete(USER_TASK_TIMESTAMP + markId);
            }

            log.debug("Cleaned up Redis data for Flux task: taskId={}, markId={}", taskId, markId);

        } catch (Exception e) {
            log.error("Cleanup Redis data error", e);
        }
    }

    /**
     * 图片处理消息数据类
     */
    public static class FluxImageProcessMessage {
        private String taskId;
        private String loginName;
        private String imageUrl;
        private Integer width;
        private Integer height;
        private Integer seed;

        // getters and setters
        public String getTaskId() {
            return taskId;
        }

        public void setTaskId(String taskId) {
            this.taskId = taskId;
        }

        public String getLoginName() {
            return loginName;
        }

        public void setLoginName(String loginName) {
            this.loginName = loginName;
        }

        public String getImageUrl() {
            return imageUrl;
        }

        public void setImageUrl(String imageUrl) {
            this.imageUrl = imageUrl;
        }

        public Integer getWidth() {
            return width;
        }

        public void setWidth(Integer width) {
            this.width = width;
        }

        public Integer getHeight() {
            return height;
        }

        public void setHeight(Integer height) {
            this.height = height;
        }

        public Integer getSeed() {
            return seed;
        }

        public void setSeed(Integer seed) {
            this.seed = seed;
        }
    }
}
