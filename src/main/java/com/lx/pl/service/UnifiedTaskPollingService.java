package com.lx.pl.service;

import com.lx.pl.dto.mq.TaskPollingVo;
import com.lx.pl.dto.flux.FluxResponse;
import com.lx.pl.dto.midjourney.MidjourneyResponse;
import com.lx.pl.enums.FluxTaskStatus;
import com.lx.pl.enums.MidjourneyTaskStatus;
import com.lx.pl.enums.TaskTypeForMq;
import com.lx.pl.mq.producer.NormalMessageProducer;
import com.lx.pl.mq.message.CommonMqMessage;
import com.lx.pl.mq.message.RMqMessage;
import com.lx.pl.config.MidjourneyConfig;
import com.lx.pl.config.FluxConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * 统一任务状态轮询服务
 * 支持MJ和Flux两种任务类型的轮询
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class UnifiedTaskPollingService {

    @Autowired
    private MidjourneyService midjourneyService;

    @Autowired
    private FluxService fluxService;

    @Autowired
    private FluxCallbackService fluxCallbackService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private NormalMessageProducer normalMessageProducer;

    @Autowired
    private MidjourneyConfig midjourneyConfig;

    @Autowired
    private FluxConfig fluxConfig;

    @Value("${rocketmq.midjourney.polling.topic:tp_midjourney_polling_test}")
    private String pollingTopic;

    @Value("${rocketmq.midjourney.polling.tag:tag_midjourney_polling_test}")
    private String pollingTag;

    private static final String TTAPI_MJ_TASK_PREFIX = "ttapi:mj:task:";
    private static final String FLUX_TASK_PREFIX = "flux:task:";

    /**
     * 启动MJ任务状态轮询
     */
    public void startMjTaskStatusPolling(String jobId, String loginName, long delaySeconds) {
        log.info("Starting MJ task status polling for jobId: {}, user: {}", jobId, loginName);

        midjourneyService.updateMjTaskStatusInRedis(jobId, 0);

        int maxAttempts = midjourneyConfig.getMaxPollingAttempts() != null ?
                midjourneyConfig.getMaxPollingAttempts() : 30;
        int pollingInterval = midjourneyConfig.getPollingIntervalSeconds() != null ?
                midjourneyConfig.getPollingIntervalSeconds() * 1000 : 2000; // 转换为毫秒

        // 创建轮询消息
        TaskPollingVo pollingVo = new TaskPollingVo(
                jobId,
                loginName,
                0, // 初始轮询次数
                maxAttempts,
                pollingInterval,
                System.currentTimeMillis()
        );

        // 发送延时消息
        sendPollingMessage(pollingVo, delaySeconds);
    }

    /**
     * 启动Flux任务状态轮询
     */
    public void startFluxTaskStatusPolling(String taskId, String loginName, Integer delaySeconds, String pollingUrl) {
        log.info("Starting Flux task status polling for taskId: {}, user: {}", taskId, loginName);

        int maxAttempts = fluxConfig.getMaxPollingAttempts() != null ?
                fluxConfig.getMaxPollingAttempts() : 10;
        int pollingInterval = fluxConfig.getPollingIntervalSeconds() != null ?
                fluxConfig.getPollingIntervalSeconds() * 1000 : 2000; // 转换为毫秒

        // 创建轮询消息
        TaskPollingVo pollingVo = new TaskPollingVo(
                TaskTypeForMq.FLUX.getType(),
                taskId,
                loginName,
                0, // 初始轮询次数
                maxAttempts,
                pollingInterval,
                System.currentTimeMillis(),
                pollingUrl
        );

        // 发送延时消息
        sendPollingMessage(pollingVo, delaySeconds.longValue());
    }

    /**
     * 发送轮询延时消息
     */
    private void sendPollingMessage(TaskPollingVo pollingVo, long delaySeconds) {
        try {
            CommonMqMessage<TaskPollingVo> mqMessage = new RMqMessage<>(
                    pollingTopic,
                    pollingTag,
                    pollingVo.getJobId() + "_" + pollingVo.getCurrentAttempt()
            );
            mqMessage.setMessage(pollingVo);

            normalMessageProducer.syncDelaySend(mqMessage, delaySeconds);

            log.debug("Sent {} polling message for taskId: {}, attempt: {}, delay: {}s",
                    pollingVo.getTaskType(), pollingVo.getJobId(), pollingVo.getCurrentAttempt(), delaySeconds);
        } catch (Exception e) {
            log.error("Failed to send {} polling message for taskId: {}",
                    pollingVo.getTaskType(), pollingVo.getJobId(), e);
        }
    }

    /**
     * 处理任务状态轮询（统一入口）
     */
    public void handleTaskStatusPolling(TaskPollingVo pollingVo) {
        String taskType = pollingVo.getTaskType();
        String taskId = pollingVo.getJobId();
        String loginName = pollingVo.getLoginName();

        log.debug("Processing {} task polling for taskId: {}, attempt: {}",
                taskType, taskId, pollingVo.getCurrentAttempt());

        try {
            // 根据任务类型调用不同的处理方法
            switch (taskType) {
                case "MJ":
                    handleMjTaskPolling(pollingVo);
                    break;
                case "FLUX":
                    handleFluxTaskPolling(pollingVo);
                    break;
                default:
                    log.warn("Unknown task type: {} for taskId: {}", taskType, taskId);
            }
        } catch (Exception e) {
            log.error("Error processing {} task polling for taskId: {}", taskType, taskId, e);
            scheduleNextPolling(pollingVo);
        }
    }

    /**
     * 处理MJ任务轮询
     */
    private void handleMjTaskPolling(TaskPollingVo pollingVo) {
        String jobId = pollingVo.getJobId();
        String loginName = pollingVo.getLoginName();

        // 检查任务是否还存在于Redis中
        String taskKey = TTAPI_MJ_TASK_PREFIX + jobId;
        if (!redisService.hasKey(taskKey) && !redisService.hasKey(jobId)) {
            log.info("MJ Task {} was cancelled or already processed by callback", jobId);
            return;
        }

        // 查询任务状态
        MidjourneyResponse.TaskStatusResponse taskStatus = midjourneyService.getTaskStatus(jobId);

        if (taskStatus == null) {
            log.warn("Failed to get MJ task status for jobId: {}, attempt: {}", jobId, pollingVo.getCurrentAttempt());
            scheduleNextPolling(pollingVo);
            return;
        }

        String status = taskStatus.getStatus();
        MidjourneyTaskStatus taskStatusEnum = MidjourneyTaskStatus.fromStatus(status);

        // 根据状态处理
        switch (taskStatusEnum) {
            case SUCCESS:
                log.info("MJ Task completed successfully - jobId: {}", jobId);
                midjourneyService.handleTaskSuccess(jobId, loginName, taskStatus);
                return; // 任务完成，不再轮询

            case FAILED:
                log.warn("MJ Task failed - jobId: {}", jobId);
                midjourneyService.handleTaskFailure(jobId, loginName);
                return; // 任务失败，不再轮询

            case PENDING_QUEUE:
            case ON_QUEUE:
                // 更新Redis状态
                midjourneyService.updateMjTaskStatusInRedis(jobId, 0);
                log.debug("MJ Task in progress - jobId: {}, status: {}", jobId, status);
                scheduleNextPolling(pollingVo);
                break;

            default:
                log.debug("MJ Task status unchanged - jobId: {}, status: {}", jobId, status);
                scheduleNextPolling(pollingVo);
                break;
        }
    }

    /**
     * 处理Flux任务轮询
     */
    private void handleFluxTaskPolling(TaskPollingVo pollingVo) {
        String taskId = pollingVo.getJobId();
        String loginName = pollingVo.getLoginName();

        // 检查任务是否还存在于Redis中
        String taskKey = FLUX_TASK_PREFIX + taskId;
        String markId = redisService.stringGet(taskKey);
        if (markId == null || markId.trim().isEmpty()) {
            log.warn("Flux task not found in Redis, stopping polling: {}", taskId);
            return;
        }

        // 获取任务状态 - 使用pollingUrl或默认方法
        FluxResponse.TaskStatusResponse taskStatus;
        if (pollingVo.getPollingUrl() != null && !pollingVo.getPollingUrl().trim().isEmpty()) {
            // 使用pollingUrl查询状态
            taskStatus = fluxService.getTaskResultByPollingUrl(pollingVo.getPollingUrl());
        } else {
            // 使用默认方法查询状态
            taskStatus = fluxService.getTaskResult(taskId);
        }

        if (taskStatus == null) {
            log.warn("Failed to get Flux task status for taskId: {}", taskId);
            scheduleNextPolling(pollingVo);
            return;
        }

        FluxTaskStatus status = FluxTaskStatus.fromStatus(taskStatus.getStatus());
        log.debug("Flux task status: taskId={}, status={}, progress={}",
                taskId, status, taskStatus.getProgress());

        // 更新Redis状态
        updateFluxTaskStatusInRedis(taskId, status, taskStatus.getProgress());

        // 根据状态处理
        switch (status) {
            case READY:
                log.info("Flux task completed successfully - taskId: {}", taskId);
                fluxService.handleTaskSuccess(taskId, loginName, taskStatus);
                return; // 任务完成，不再轮询

            case ERROR:
            case CANCELLED:
            case TIMEOUT:
                log.warn("Flux task failed - taskId: {}, status: {}", taskId, status);
                fluxService.handleTaskFailure(taskId, loginName);
                return; // 任务失败，不再轮询

            case PENDING:
            case RUNNING:
                log.debug("Flux task in progress - taskId: {}, status: {}, progress: {}",
                        taskId, status, taskStatus.getProgress());
                scheduleNextPolling(pollingVo);
                break;

            default:
                log.debug("Flux task status unchanged - taskId: {}, status: {}", taskId, status);
                scheduleNextPolling(pollingVo);
                break;
        }
    }

    /**
     * 更新Flux任务状态到Redis
     */
    private void updateFluxTaskStatusInRedis(String taskId, FluxTaskStatus status, Integer progress) {
        try {
            String markId = redisService.stringGet(taskId);
            if (markId == null || markId.trim().isEmpty()) {
                return;
            }

            String loginName = redisService.stringGet(markId);
            if (loginName == null || loginName.trim().isEmpty()) {
                return;
            }

            // 根据状态设置Redis值
            int redisStatus;
            switch (status) {
                case PENDING:
                    redisStatus = 1; // 排队中
                    break;
                case RUNNING:
                    redisStatus = 0; // 执行中
                    break;
                case READY:
                    redisStatus = 3; // 成功
                    break;
                case ERROR:
                case CANCELLED:
                case TIMEOUT:
                case TASK_NOT_FOUND:
                    redisStatus = 2; // 失败
                    break;
                default:
                    redisStatus = -1; // 未知
            }

            redisService.putDataToHash(loginName, markId, redisStatus, 2, java.util.concurrent.TimeUnit.HOURS);

            log.debug("Updated Flux task status in Redis: taskId={}, status={}, redisStatus={}",
                    taskId, status, redisStatus);

        } catch (Exception e) {
            log.error("Update Flux task status in Redis error", e);
        }
    }

    /**
     * 安排下一次轮询
     */
    private void scheduleNextPolling(TaskPollingVo pollingVo) {
        int nextAttempt = pollingVo.getCurrentAttempt() + 1;

        if (nextAttempt >= pollingVo.getMaxAttempts()) {
            // 超时处理
            log.warn("{} task status polling timeout for taskId: {} after {} attempts",
                    pollingVo.getTaskType(), pollingVo.getJobId(), pollingVo.getMaxAttempts());

            // 根据任务类型调用不同的超时处理方法
            switch (pollingVo.getTaskType()) {
                case "MJ":
                    midjourneyService.handleTaskTimeout(pollingVo.getJobId(), pollingVo.getLoginName());
                    break;
                case "FLUX":
                    fluxService.handleTaskFailure(pollingVo.getJobId(), pollingVo.getLoginName());
                    break;
                default:
                    log.warn("Unknown task type: {} for taskId: {}", pollingVo.getTaskType(), pollingVo.getJobId());
                    return;
            }

            // 创建下一次轮询消息
            TaskPollingVo nextPollingVo = new TaskPollingVo();
            nextPollingVo.setTaskType(pollingVo.getTaskType());
            nextPollingVo.setJobId(pollingVo.getJobId());
            nextPollingVo.setLoginName(pollingVo.getLoginName());
            nextPollingVo.setCurrentAttempt(nextAttempt);
            nextPollingVo.setMaxAttempts(pollingVo.getMaxAttempts());
            nextPollingVo.setPollingInterval(pollingVo.getPollingInterval());
            nextPollingVo.setCreateTimestamp(pollingVo.getCreateTimestamp());
            nextPollingVo.setPollingUrl(pollingVo.getPollingUrl());

            // 发送延时消息，延时时间为轮询间隔（毫秒转秒）
            long delaySeconds = pollingVo.getPollingInterval() / 1000;
            sendPollingMessage(nextPollingVo, delaySeconds);
        }
    }
}
